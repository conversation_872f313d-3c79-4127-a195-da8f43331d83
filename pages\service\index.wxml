<custom-modal
  show="{{modalShow}}"
  title="{{modalConfig.title}}"
  content="{{modalConfig.content}}"
  buttons="{{modalConfig.buttons}}"
  bind:modalConfirm="modalConfirmHandler"
  bind:modalCancel="modalCancelHandler"
/>
<view class="container servicepage">
  <custom-navbar currentCode="{{currentTabCode}}" bind:tabchange="handleTabChange" />
  <view class="flex flex-wrap diygw-col-0 items-center justify-between choose-pet">
    <view class="flex flex-nowrap">
      <view wx:if="{{pets}}" class="flex items-center add-pet" style="background-color: {{pets.gender===1 ? 'rgba(122, 221, 252, 0.3)':pets.gender===0 ?'rgba(255, 251, 220, 1)':'rgba(255, 192, 218, 0.3)'}};" bind:tap="addCurrentPet">
        <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center">
          <image src="{{pets.avatar||siteinfo.constant.pet[pets.type]}}" class="image7-size diygw-image diygw-col-0 image7-clz" mode="widthFix"></image>
        </view>
        <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-between flex48-clz">
          <text class="diygw-col-0 text23-clz"> {{pets.name}} </text>
        </view>
        <image class="exchangeIcon" src='https://xian7.zos.ctyun.cn/pet/static/exchange-b.png' />
      </view>
      <view wx:else bind:tap="addCurrentPet">
        <view class="flex items-center add-pet">
          <text class="flex icon8 icon8-clz diy-icon-add"></text>
          选择服务宠物
        </view>
      </view>
    </view>
    <image src='https://xian7.zos.ctyun.cn/pet/static/pet.png' />
  </view>

  <view class="flex serviceTab diygw-col-24 flex-direction-row">
    <view class="diygw-tabs text-center cale-title small-border tabs1-title">
      <view class="diygw-tab-item tabs1-item-title {{item.id==typeId?'current':''}}" wx:for="{{typesList}}" wx:key="id" wx:for-item="item" wx:for-index="index" data-key="index" data-index="{{index}}" data-id="{{item.id}}" catchtap="changeType">
        <text class="flex icon diygw-col-0 diy-icon-titles"></text>
        <view class='type'>{{item.type}}</view>
        <view class='type'>{{item.subtype}}</view>
      </view>
    </view>
    <view class="tab-content">
      <view class="tab-content-title">{{currentType.type}}</view>
      <view wx:if="{{serviceList.length}}">
        <view wx:for="{{serviceList}}" wx:key="id" wx:for-item="item" wx:for-index="index" data-key="index" data-index="{{index}}" class="flex-card" data-item="{{item}}" data-serviceId="{{id}}" bind:tap="redirect">
          <view class="flex diygw-col-24 items-stretch flex-nowrap">
            <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center">
              <image src="{{item.logo}}" class="image-size diygw-image diygw-col-0" mode="widthFix"></image>
            </view>
            <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-between right-content">
              <view>
                <text class="diygw-col-0 text-bold"> {{item.serviceName}} </text>
                <view class="flex flex-wrap diygw-col-24 text-grey">
                  {{item.weightDescription}}
                </view>
              </view>
              <view class="flex flex-wrap diygw-col-24 justify-between">
                <view class="flex flex-wrap diygw-col-0 items-center item-price-wrap">
                  <text class="flex icon8 diygw-col-0 icon8-clz diy-icon-recharge"></text>
                  <text class="diygw-text-line1 diygw-col-0 text-price"> {{item.basePrice}} </text>
                </view>
                <view class="flex flex-wrap diygw-col-0 items-center">
                  <text class="flex icon8 diygw-col-0 icon8-clz diy-icon-roundright"></text>
                </view>
              </view>
            </view>
          </view>
          <view class="clearfix"></view>
        </view>
      </view>
      <view wx:else class="flex flex-wrap diygw-col-24 items-center justify-center direction-column">
        <empty icon="//xian7.zos.ctyun.cn/pet/static/nogoods.png" text="暂无服务" class="empty" />
      </view>
    </view>
  </view>
  <view class="clearfix"></view>
  <custom-tabbar currentActive='service' />
</view>