export default {
  debug: true,
  // 基础配置
  baseUrl: "http://localhost:3001",
  // baseUrl: "https://manager.petsjoylife.com/api",
  // baseUrl: "https://test.xdpb.top/api",
  timeout: 10000,
  zos_endpoint: "xian7.zos.ctyun.cn",
  zos_bucket: "pet",

  // API路径配置
  apiUrls: {
    // 天翼云
    zos: {
      getUploadLink: "/openapi/zos/upload-link", // 获取上传链接
      setObjHeaders: "/openapi/zos/set-object-headers", // 设置对象的 HTTP 头
      setObjAcl: "/openapi/zos/set-object-acl", // 获设置对象ACL
    },
    // 字典
    dictionary: {
      list: "/openapi/dictionary", // 查询字典列表
    },
    // 定位
    location: {
      findNearbyVehicles: "/openapi/location/findNearbyVehicles", // 查找附近车辆
      calculateDistance: "/openapi/location/calculateDistance", // 计算两个经纬度之间的距离
    },
    // 区域模拟
    area: {
      list: "/openapi/areas", // 查询所有行政区
    },
    // 用户模块
    user: {
      getPhoneNumber: "/openapi/weapp/getPhoneNumber", // 获取手机号
      register: "/openapi/user/register", // 注册
      login: "/openapi/weapp/code2Session", // 登录
      updateProfile: "/openapi/user/update", // 更新用户信息，未验证
      uploadAvatar: "/openapi/user/profile", // 上传用户头像，未验证
      getPets: "/customers/{id}/pets", // 查询用户已有宠物
      addPet: "/customers/{id}/pet", // 新增宠物
      editPet: "/customers/{id}/pet/{petId}", // 编辑宠物
      delPet: "/customers/{id}/pet/{petId}", // 删除宠物
    },
    // 地址模块
    address: {
      list: "/customers/{id}/addresses", // 获取客户的地址列表
      add: "/customers/{id}/address", // 增加地址
      update: "/customers/{id}/address/{addressId}", // 编辑地址
      delete: "/customers/{id}/address/{addressId}", // 删除地址
      default: "/customers/{id}/addresses/default", // 获取客户的默认地址
      active: "/customers/{id}/address/{addressId}/default", // 设为默认
    },
    // 服务模块
    service: {
      list: "/openapi/service/types", // 获取服务类目列表
      services: "/openapi/service/services/{typeId}", // 获取指定类目下的服务列表
      additionalService: "/openapi/service/additional-service/{serviceId}", // 获取增项服务
    },
    // 订单模块
    order: {
      list: "/customers/{id}/orders", // 获取订单列表
      detail: "/customers//{id}/order/{orderId}", // 获取订单详情
      add: "/customers/{id}/order", // 新增订单
      pay: "/customers/{customerId}/order/{sn}/pay", // 支付订单
      status: "/customers/{customerId}/order/{sn}/status", // 获取订单状态
      refundRequest: "/customers/{customerId}/applyRefund/{sn}", // 申请退款
      cancel: "/customers/{customerId}/order/{orderId}", // 取消订单
    },
    pay: {
      jsapi: "/wepay/jsapi", // 下单
      pay_sign: "/wepay/pay_sign", // 获取支付签名
      getTransactionsBySN: "/wepay/transactions/sn/:sn", // 商户订单号查询订单
      refund: "/wepay/refund/:sn", // 退款
    },
    // 权益卡
    rightsCard: {
      list: "/openapi/rights-card", // 查询权限卡类列表
      buy: "/membership-card-orders", // 购买权益卡
      pay: "/membership-card-orders/:sn/pay", // 完成支付
      myCards: "/customers/{customerId}/membership-cards", // 查询我的权益卡
      myValidCards: "/customers/{customerId}/valid-membership-cards", // 获取我的有效权益卡列表
      availableCards: "/customers/{customerId}/available-membership-cards" // 获取可用的权益卡
    },
    // 优惠券
    coupon: {
      list: "/openapi/coupon", // 查询优惠券列表
      buy: "/coupon-orders", // 购买优惠券
      pay: "/coupon-orders/:sn/pay", // 完成支付
      myCoupons: "/customers/{customerId}/coupons", // 查询我的优惠券
      myValidCoupons: "/customers/{customerId}/valid-coupons", // 获取我的有效优惠券表
      availableCoupons: "/customers/{customerId}/available-coupons" // 获取可用的优惠券
    },
    // 推广记录
    promotionRecord: {
      create: '/promotion-record/', // 创建推广记录
    },
    // 消息
    weMessage: {
      subscription: '/subscription', // 订阅
    },
  },
};
