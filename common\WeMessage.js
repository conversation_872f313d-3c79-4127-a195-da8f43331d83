/**
 * 消息订阅
 */

import { OrderStatus } from './constant';
import messageApi from '../api/modules/weMessage';

class WeMessage {
  constructor(openId, sn, orderStatus) {
    this.openId = openId;
    this.sn = sn;
    this.orderStatus = orderStatus;
    this.templateIds = {
      接单成功通知: 'LSBwIETgaGdTBEOSE2jWc9H4kSqV6kmaLbYubaj98Gk',
    };
  }

  /** 订单支付成功后调用此函数 */
  handlePaymentSuccess() {
    if (this.orderStatus === OrderStatus.待接单) {
      return {
        modalConfig: {
          title: '接单提醒',
          content: '您的订单已支付成功，是否接收商家接单提醒？',
          buttons: [
            {
              text: '暂不需要',
              type: 'cancel',
              event: 'modalCancel'
            },
            {
              text: '同意接收',
              type: 'primary',
              event: 'modalConfirm'
            }
          ]
        }
      };
    }
    return null;
  }

  /** 请求订阅接单提醒 */
  requestOrderConfirmationSubscribe() {
    wx.requestSubscribeMessage({
      tmplIds: [this.templateIds.接单成功通知], // 替换为你的接单提醒模板ID
      success: async res => {
        if (res[this.templateIds.接单成功通知] === 'accept') {
          // 用户同意订阅，记录订阅状态到服务器
          await this.saveSubscriptionStatus(true);
          wx.showToast({
            title: '已开启接单提醒',
            icon: 'success',
          });
        } else if (res[this.templateIds.接单成功通知] === 'reject') {
          // 用户拒绝订阅，记录状态
          await this.saveSubscriptionStatus(false);
          wx.showToast({
            title: '您可以在需要时手动开启提醒',
            icon: 'none',
          });
        }
      },
      fail: err => {
        console.error('订阅消息请求失败:', err);
        wx.showToast({
          title: '订阅失败，请稍后再试',
          icon: 'none',
        });
      },
    });
  }

  /** 保存订阅状态到服务器 */
  async saveSubscriptionStatus(isSubscribed) {
    const res = await messageApi.subscription(this.openId, this.sn, isSubscribed, this.templateIds.接单成功通知);
    console.log('订阅状态保存结果： ', res);
  }
}

export default WeMessage;
